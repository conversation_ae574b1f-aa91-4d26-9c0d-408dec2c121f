# API路由变更说明

## 🎯 变更目标

将所有API路由中的 `admin/` 路径前缀去掉，使路由更简洁。

## 📋 路由变更对照表

### 认证相关路由
| 原路径 | 新路径 | 说明 |
|--------|--------|------|
| `POST /api/admin/auth/login` | `POST /api/auth/login` | 用户登录 |
| `GET /api/admin/auth/check` | `GET /api/auth/check` | 检查登录状态 |
| `POST /api/admin/auth/logout` | `POST /api/auth/logout` | 用户登出 |
| `GET /api/admin/auth/info` | `GET /api/auth/info` | 获取用户信息 |

### 用户管理路由
| 原路径 | 新路径 | 说明 |
|--------|--------|------|
| `GET /api/admin/users` | `GET /api/users` | 用户列表 |
| `POST /api/admin/users` | `POST /api/users` | 创建用户 |
| `GET /api/admin/users/{id}` | `GET /api/users/{id}` | 获取用户详情 |
| `PUT /api/admin/users/{id}` | `PUT /api/users/{id}` | 更新用户 |
| `DELETE /api/admin/users/{id}` | `DELETE /api/users/{id}` | 删除用户 |
| `POST /api/admin/users/{id}/password` | `POST /api/users/{id}/password` | 修改密码 |

### 文章分类管理路由
| 原路径 | 新路径 | 说明 |
|--------|--------|------|
| `GET /api/admin/categories` | `GET /api/categories` | 分类列表 |
| `POST /api/admin/categories` | `POST /api/categories` | 创建分类 |
| `GET /api/admin/categories/tree` | `GET /api/categories/tree` | 获取菜单树 |
| `GET /api/admin/categories/children` | `GET /api/categories/children` | 获取子分类 |
| `GET /api/admin/categories/type/{type}` | `GET /api/categories/type/{type}` | 根据类型获取分类 |
| `GET /api/admin/categories/{id}` | `GET /api/categories/{id}` | 获取分类详情 |
| `PUT /api/admin/categories/{id}` | `PUT /api/categories/{id}` | 更新分类 |
| `DELETE /api/admin/categories/{id}` | `DELETE /api/categories/{id}` | 删除分类 |
| `GET /api/admin/categories/{id}/breadcrumb` | `GET /api/categories/{id}/breadcrumb` | 获取面包屑 |
| `POST /api/admin/categories/{id}/move` | `POST /api/categories/{id}/move` | 移动分类 |
| `POST /api/admin/categories/{id}/toggle-show` | `POST /api/categories/{id}/toggle-show` | 切换显示状态 |
| `POST /api/admin/categories/update-sort` | `POST /api/categories/update-sort` | 批量更新排序 |

### 文章管理路由
| 原路径 | 新路径 | 说明 |
|--------|--------|------|
| `GET /api/admin/articles` | `GET /api/articles` | 文章列表 |
| `POST /api/admin/articles` | `POST /api/articles` | 创建文章 |
| `GET /api/admin/articles/statistics` | `GET /api/articles/statistics` | 文章统计 |
| `GET /api/admin/articles/{id}` | `GET /api/articles/{id}` | 获取文章详情 |
| `PUT /api/admin/articles/{id}` | `PUT /api/articles/{id}` | 更新文章 |
| `DELETE /api/admin/articles/{id}` | `DELETE /api/articles/{id}` | 删除文章 |
| `POST /api/admin/articles/{id}/toggle-top` | `POST /api/articles/{id}/toggle-top` | 切换置顶状态 |
| `POST /api/admin/articles/{id}/change-status` | `POST /api/articles/{id}/change-status` | 切换文章状态 |
| `POST /api/admin/articles/update-sort` | `POST /api/articles/update-sort` | 批量更新排序 |

### 文章自定义字段管理路由
| 原路径 | 新路径 | 说明 |
|--------|--------|------|
| `GET /api/admin/article-custom-fields` | `GET /api/article-custom-fields` | 字段列表 |
| `GET /api/admin/article-custom-fields/active` | `GET /api/article-custom-fields/active` | 获取启用字段 |
| `POST /api/admin/article-custom-fields` | `POST /api/article-custom-fields` | 创建字段 |
| `GET /api/admin/article-custom-fields/statistics` | `GET /api/article-custom-fields/statistics` | 字段统计 |
| `GET /api/admin/article-custom-fields/type/{type}` | `GET /api/article-custom-fields/type/{type}` | 根据类型获取字段 |
| `GET /api/admin/article-custom-fields/{id}` | `GET /api/article-custom-fields/{id}` | 获取字段详情 |
| `PUT /api/admin/article-custom-fields/{id}` | `PUT /api/article-custom-fields/{id}` | 更新字段 |
| `DELETE /api/admin/article-custom-fields/{id}` | `DELETE /api/article-custom-fields/{id}` | 删除字段 |
| `POST /api/admin/article-custom-fields/{id}/toggle-active` | `POST /api/article-custom-fields/{id}/toggle-active` | 切换启用状态 |
| `GET /api/admin/article-custom-fields/{id}/usage-stats` | `GET /api/article-custom-fields/{id}/usage-stats` | 字段使用统计 |
| `POST /api/admin/article-custom-fields/update-sort` | `POST /api/article-custom-fields/update-sort` | 批量更新排序 |

### 门户配置管理路由
| 原路径 | 新路径 | 说明 |
|--------|--------|------|
| `GET /api/admin/portal/configs/` | `GET /api/portal/configs/` | 获取配置列表 |
| `GET /api/admin/portal/configs/groups` | `GET /api/portal/configs/groups` | 获取配置分组 |
| `GET /api/admin/portal/configs/group/{group}` | `GET /api/portal/configs/group/{group}` | 根据分组获取配置 |
| `GET /api/admin/portal/configs/{id}` | `GET /api/portal/configs/{id}` | 获取单个配置 |
| `POST /api/admin/portal/configs/` | `POST /api/portal/configs/` | 创建配置 |
| `PUT /api/admin/portal/configs/{id}` | `PUT /api/portal/configs/{id}` | 更新配置 |
| `DELETE /api/admin/portal/configs/{id}` | `DELETE /api/portal/configs/{id}` | 删除配置 |
| `POST /api/admin/portal/configs/batch` | `POST /api/portal/configs/batch` | 批量设置配置 |
| `POST /api/admin/portal/configs/clear-cache` | `POST /api/portal/configs/clear-cache` | 清除缓存 |

### 门户模块管理路由
| 原路径 | 新路径 | 说明 |
|--------|--------|------|
| `GET /api/admin/portal/modules/` | `GET /api/portal/modules/` | 获取模块列表 |
| `GET /api/admin/portal/modules/{id}` | `GET /api/portal/modules/{id}` | 获取单个模块 |
| `POST /api/admin/portal/modules/` | `POST /api/portal/modules/` | 创建模块 |
| `PUT /api/admin/portal/modules/{id}` | `PUT /api/portal/modules/{id}` | 更新模块 |
| `DELETE /api/admin/portal/modules/{id}` | `DELETE /api/portal/modules/{id}` | 删除模块 |
| `POST /api/admin/portal/modules/{id}/toggle` | `POST /api/portal/modules/{id}/toggle` | 切换模块状态 |
| `POST /api/admin/portal/modules/sort` | `POST /api/portal/modules/sort` | 更新排序 |
| `GET /api/admin/portal/modules/{module_name}/config` | `GET /api/portal/modules/{module_name}/config` | 获取模块配置 |
| `POST /api/admin/portal/modules/{module_name}/config` | `POST /api/portal/modules/{module_name}/config` | 设置模块配置 |

### 公开接口路由变更
| 原路径 | 新路径 | 说明 |
|--------|--------|------|
| `GET /api/categories/public/tree` | `GET /api/public-categories/public/tree` | 公开菜单树 |
| `GET /api/categories/public/list` | `GET /api/public-categories/public/list` | 公开分类列表 |
| `GET /api/categories/admin/stats` | `GET /api/public-categories/stats` | 管理员统计 |
| `POST /api/categories/system/manage` | `POST /api/public-categories/system/manage` | 系统管理 |
| `GET /api/portal/modules/enabled` | `GET /api/public-portal/modules/enabled` | 获取启用的模块列表 |

## 🔧 需要更新的地方

### 1. 前端代码
需要更新所有前端JavaScript代码中的API调用路径：
- 管理后台的所有AJAX请求
- 前端页面的API调用

### 2. 文档更新
需要更新以下文档：
- API接口文档
- 开发者指南
- 集成说明

### 3. 测试代码
需要更新所有测试代码中的API路径。

## ⚠️ 注意事项

1. **向后兼容性**：此变更会破坏现有的API调用，需要同步更新前端代码
2. **权限验证**：所有路由的权限验证逻辑保持不变
3. **中间件**：所有中间件配置保持不变
4. **控制器**：控制器文件和方法名称保持不变，只是路由路径发生变化

## 🚀 迁移建议

1. **分阶段迁移**：建议分模块逐步迁移，避免一次性修改导致的问题
2. **测试验证**：每个模块迁移后都要进行完整的功能测试
3. **文档同步**：及时更新相关文档和示例代码

---

**变更时间**：2025年1月11日  
**变更人员**：Claude 4.0 sonnet 🐾
