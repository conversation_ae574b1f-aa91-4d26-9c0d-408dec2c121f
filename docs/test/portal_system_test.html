<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>门户配置系统测试页面</title>
    <link rel="stylesheet" href="/static/layui/css/layui.css">
    <style>
        .test-container {
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            border: 1px solid #e6e6e6;
            border-radius: 6px;
            padding: 20px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
        }
        .api-result {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            color: #52c41a;
            font-weight: bold;
        }
        .error {
            color: #f5222d;
            font-weight: bold;
        }
        .module-preview {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            background-color: #fafafa;
        }
        .module-one-column {
            width: 100%;
        }
        .module-two-column {
            width: 48%;
            display: inline-block;
            margin-right: 2%;
            vertical-align: top;
        }
        .config-item {
            margin: 5px 0;
            padding: 5px 10px;
            background-color: #f0f0f0;
            border-radius: 3px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="layui-container test-container">
        <div class="layui-card">
            <div class="layui-card-header">
                <h1>门户配置系统功能测试</h1>
                <p>此页面用于测试门户配置系统的各项功能，包括API接口和前端展示效果。</p>
            </div>
            <div class="layui-card-body">
                
                <!-- 基础配置测试 -->
                <div class="test-section">
                    <div class="test-title">🔧 基础配置测试</div>
                    <div class="layui-row">
                        <div class="layui-col-md6">
                            <button class="layui-btn layui-btn-sm" onclick="testGetConfigs()">获取配置列表</button>
                            <button class="layui-btn layui-btn-sm" onclick="testGetConfigGroups()">获取配置分组</button>
                            <button class="layui-btn layui-btn-sm" onclick="testGetBasicConfigs()">获取基础配置</button>
                        </div>
                        <div class="layui-col-md6">
                            <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="testCreateConfig()">创建测试配置</button>
                            <button class="layui-btn layui-btn-sm layui-btn-warm" onclick="testBatchSetConfigs()">批量设置配置</button>
                        </div>
                    </div>
                    <div id="configTestResult" class="api-result"></div>
                </div>

                <!-- 模块配置测试 -->
                <div class="test-section">
                    <div class="test-title">🧩 模块配置测试</div>
                    <div class="layui-row">
                        <div class="layui-col-md6">
                            <button class="layui-btn layui-btn-sm" onclick="testGetModules()">获取模块列表</button>
                            <button class="layui-btn layui-btn-sm" onclick="testGetEnabledModules()">获取启用模块</button>
                            <button class="layui-btn layui-btn-sm" onclick="testGetModuleConfig()">获取模块配置</button>
                        </div>
                        <div class="layui-col-md6">
                            <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="testCreateModule()">创建测试模块</button>
                            <button class="layui-btn layui-btn-sm layui-btn-warm" onclick="testUpdateModuleConfig()">更新模块配置</button>
                        </div>
                    </div>
                    <div id="moduleTestResult" class="api-result"></div>
                </div>

                <!-- 前端展示测试 -->
                <div class="test-section">
                    <div class="test-title">🎨 前端展示测试</div>
                    <button class="layui-btn layui-btn-sm" onclick="testRenderModules()">渲染模块展示</button>
                    <button class="layui-btn layui-btn-sm" onclick="testApplyConfigs()">应用基础配置</button>
                    <div id="modulePreview" style="margin-top: 15px;"></div>
                    <div id="configPreview" style="margin-top: 15px;"></div>
                </div>

                <!-- 权限测试 -->
                <div class="test-section">
                    <div class="test-title">🔐 权限测试</div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">测试Token:</label>
                        <div class="layui-input-inline" style="width: 300px;">
                            <input type="text" id="testToken" placeholder="请输入管理员Token" class="layui-input">
                        </div>
                        <button class="layui-btn layui-btn-sm" onclick="setTestToken()">设置Token</button>
                        <button class="layui-btn layui-btn-sm layui-btn-primary" onclick="clearTestToken()">清除Token</button>
                    </div>
                    <div id="authTestResult" class="api-result"></div>
                </div>

            </div>
        </div>
    </div>

    <script src="/static/layui/layui.js"></script>
    <script>
        layui.use(['layer'], function(){
            var layer = layui.layer;
            var $ = layui.$;

            // 获取Token
            function getToken() {
                return localStorage.getItem('admin_token') || $('#testToken').val() || '';
            }

            // 设置测试Token
            window.setTestToken = function() {
                var token = $('#testToken').val();
                if (token) {
                    localStorage.setItem('admin_token', token);
                    layer.msg('Token设置成功');
                    $('#authTestResult').html('<span class="success">Token已设置: ' + token.substring(0, 20) + '...</span>');
                } else {
                    layer.msg('请输入Token');
                }
            };

            // 清除测试Token
            window.clearTestToken = function() {
                localStorage.removeItem('admin_token');
                $('#testToken').val('');
                layer.msg('Token已清除');
                $('#authTestResult').html('<span class="error">Token已清除</span>');
            };

            // 通用API请求函数
            function apiRequest(url, method = 'GET', data = null, needAuth = true) {
                var headers = {
                    'Content-Type': 'application/json'
                };
                
                if (needAuth) {
                    var token = getToken();
                    if (token) {
                        headers['Authorization'] = 'Bearer ' + token;
                    }
                }

                return fetch(url, {
                    method: method,
                    headers: headers,
                    body: data ? JSON.stringify(data) : null
                }).then(response => response.json());
            }

            // 显示结果
            function showResult(elementId, result, title = '') {
                var html = '';
                if (title) {
                    html += '<strong>' + title + '</strong><br>';
                }
                html += '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
                document.getElementById(elementId).innerHTML = html;
            }

            // 基础配置测试函数
            window.testGetConfigs = function() {
                apiRequest('/api/portal/configs/?page=1&limit=5')
                    .then(result => showResult('configTestResult', result, '获取配置列表:'))
                    .catch(error => showResult('configTestResult', {error: error.message}, '请求失败:'));
            };

            window.testGetConfigGroups = function() {
                apiRequest('/api/portal/configs/groups')
                    .then(result => showResult('configTestResult', result, '获取配置分组:'))
                    .catch(error => showResult('configTestResult', {error: error.message}, '请求失败:'));
            };

            window.testGetBasicConfigs = function() {
                apiRequest('/api/portal/configs/group/basic')
                    .then(result => showResult('configTestResult', result, '获取基础配置:'))
                    .catch(error => showResult('configTestResult', {error: error.message}, '请求失败:'));
            };

            window.testCreateConfig = function() {
                var testConfig = {
                    config_key: 'test_config_' + Date.now(),
                    config_value: 'test value',
                    config_type: 'string',
                    group_name: 'test',
                    description: '测试配置项'
                };
                
                apiRequest('/api/portal/configs/', 'POST', testConfig)
                    .then(result => showResult('configTestResult', result, '创建测试配置:'))
                    .catch(error => showResult('configTestResult', {error: error.message}, '请求失败:'));
            };

            window.testBatchSetConfigs = function() {
                var batchConfigs = {
                    configs: {
                        'batch_test_1': 'value1',
                        'batch_test_2': 123,
                        'batch_test_3': true
                    },
                    group: 'batch_test'
                };
                
                apiRequest('/api/portal/configs/batch', 'POST', batchConfigs)
                    .then(result => showResult('configTestResult', result, '批量设置配置:'))
                    .catch(error => showResult('configTestResult', {error: error.message}, '请求失败:'));
            };

            // 模块配置测试函数
            window.testGetModules = function() {
                apiRequest('/api/portal/modules/?page=1&limit=5')
                    .then(result => showResult('moduleTestResult', result, '获取模块列表:'))
                    .catch(error => showResult('moduleTestResult', {error: error.message}, '请求失败:'));
            };

            window.testGetEnabledModules = function() {
                apiRequest('/api/portal/modules/enabled', 'GET', null, false)
                    .then(result => showResult('moduleTestResult', result, '获取启用模块:'))
                    .catch(error => showResult('moduleTestResult', {error: error.message}, '请求失败:'));
            };

            window.testGetModuleConfig = function() {
                apiRequest('/api/portal/modules/news_banner/config')
                    .then(result => showResult('moduleTestResult', result, '获取模块配置:'))
                    .catch(error => showResult('moduleTestResult', {error: error.message}, '请求失败:'));
            };

            window.testCreateModule = function() {
                var testModule = {
                    module_name: 'test_module_' + Date.now(),
                    module_title: '测试模块',
                    module_description: '这是一个测试模块',
                    column_count: 1,
                    config_data: {
                        limit: 5,
                        show_title: true,
                        test_option: 'test_value'
                    },
                    is_enabled: true
                };
                
                apiRequest('/api/portal/modules/', 'POST', testModule)
                    .then(result => showResult('moduleTestResult', result, '创建测试模块:'))
                    .catch(error => showResult('moduleTestResult', {error: error.message}, '请求失败:'));
            };

            window.testUpdateModuleConfig = function() {
                var configData = {
                    config_data: {
                        limit: 8,
                        show_image: true,
                        auto_play: false,
                        updated_at: new Date().toISOString()
                    }
                };
                
                apiRequest('/api/portal/modules/news_banner/config', 'POST', configData)
                    .then(result => showResult('moduleTestResult', result, '更新模块配置:'))
                    .catch(error => showResult('moduleTestResult', {error: error.message}, '请求失败:'));
            };

            // 前端展示测试函数
            window.testRenderModules = function() {
                apiRequest('/api/portal/modules/enabled', 'GET', null, false)
                    .then(result => {
                        if (result.code === 200) {
                            renderModulesPreview(result.data);
                        } else {
                            $('#modulePreview').html('<span class="error">获取模块失败: ' + result.message + '</span>');
                        }
                    })
                    .catch(error => {
                        $('#modulePreview').html('<span class="error">请求失败: ' + error.message + '</span>');
                    });
            };

            window.testApplyConfigs = function() {
                apiRequest('/api/portal/configs/group/basic')
                    .then(result => {
                        if (result.code === 200) {
                            renderConfigsPreview(result.data);
                        } else {
                            $('#configPreview').html('<span class="error">获取配置失败: ' + result.message + '</span>');
                        }
                    })
                    .catch(error => {
                        $('#configPreview').html('<span class="error">请求失败: ' + error.message + '</span>');
                    });
            };

            // 渲染模块预览
            function renderModulesPreview(modules) {
                var html = '<h4>模块展示预览:</h4>';
                
                modules.forEach(function(module) {
                    var columnClass = module.column_count === 2 ? 'module-two-column' : 'module-one-column';
                    var statusText = module.is_enabled ? '启用' : '禁用';
                    var statusClass = module.is_enabled ? 'success' : 'error';
                    
                    html += '<div class="module-preview ' + columnClass + '">';
                    html += '<h5>' + module.module_title + ' <span class="' + statusClass + '">(' + statusText + ')</span></h5>';
                    html += '<p><strong>模块名:</strong> ' + module.module_name + '</p>';
                    html += '<p><strong>列数:</strong> ' + module.column_count + '</p>';
                    html += '<p><strong>描述:</strong> ' + (module.module_description || '无') + '</p>';
                    
                    if (module.config_data) {
                        html += '<p><strong>配置:</strong></p>';
                        for (var key in module.config_data) {
                            html += '<div class="config-item">' + key + ': ' + JSON.stringify(module.config_data[key]) + '</div>';
                        }
                    }
                    
                    html += '</div>';
                });
                
                $('#modulePreview').html(html);
            }

            // 渲染配置预览
            function renderConfigsPreview(configs) {
                var html = '<h4>基础配置预览:</h4>';
                
                for (var key in configs) {
                    var config = configs[key];
                    html += '<div class="config-item">';
                    html += '<strong>' + key + '</strong>: ' + JSON.stringify(config.value);
                    html += ' <small>(' + config.type + ')</small>';
                    if (config.description) {
                        html += ' - ' + config.description;
                    }
                    html += '</div>';
                }
                
                $('#configPreview').html(html);
            }

            // 页面加载时显示当前Token状态
            $(document).ready(function() {
                var token = getToken();
                if (token) {
                    $('#testToken').val(token);
                    $('#authTestResult').html('<span class="success">当前Token: ' + token.substring(0, 20) + '...</span>');
                } else {
                    $('#authTestResult').html('<span class="error">未设置Token，部分功能需要管理员权限</span>');
                }
            });
        });
    </script>
</body>
</html>
