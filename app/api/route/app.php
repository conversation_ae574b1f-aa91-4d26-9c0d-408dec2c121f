<?php
use think\facade\Route;

// API根路径
Route::get('/', 'app\api\controller\TestController@index');

// 测试路由
Route::get('test', 'app\api\controller\TestController@index');
Route::get('health', 'app\api\controller\TestController@health');

// 认证路由
Route::group('auth', function () {
    Route::get('check', 'app\api\controller\AuthController@check');
    Route::post('login', 'app\api\controller\AuthController@login');
    Route::get('login', 'app\api\controller\AuthController@login'); // 同时支持GET请求用于测试
});

// 分类管理路由
Route::group('categories', function () {
    Route::get('/', 'app\api\controller\CategoryController@index');
    Route::post('/', 'app\api\controller\CategoryController@create');
    Route::get('<id>', 'app\api\controller\CategoryController@read');
    Route::put('<id>', 'app\api\controller\CategoryController@update');
    Route::delete('<id>', 'app\api\controller\CategoryController@delete');
});

// 文章管理路由
Route::group('articles', function () {
    Route::get('/', 'app\api\controller\SimpleArticleController@index');
    Route::get('hot', 'app\api\controller\SimpleArticleController@hot');
    Route::get('latest', 'app\api\controller\SimpleArticleController@latest');
    Route::get('banner', 'app\api\controller\SimpleArticleController@banner');
    Route::get('<id>', 'app\api\controller\SimpleArticleController@read');
});

// 公开门户API路由
Route::group('public-portal', function () {
    Route::get('modules/enabled', 'app\api\controller\PublicPortalController@enabledModules');
    Route::get('configs/<group>', 'app\api\controller\PublicPortalController@configsByGroup');
    Route::get('home-data', 'app\api\controller\PublicPortalController@homeData');
});
