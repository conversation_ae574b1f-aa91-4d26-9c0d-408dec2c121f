<?php
use think\facade\Route;

// API根路径
Route::get('/', 'app\api\controller\TestController@index');

// 测试路由
Route::get('test', 'app\api\controller\TestController@index');
Route::get('health', 'app\api\controller\TestController@health');

// 认证路由
Route::group('auth', function () {
    Route::get('check', 'app\api\controller\AuthController@check');
    Route::post('login', 'app\api\controller\AuthController@login');
    Route::get('login', 'app\api\controller\AuthController@login'); // 同时支持GET请求用于测试
});
