<?php
// api应用路由定义文件

use think\facade\Route;

// 测试路由
Route::get('test/bean', 'Test/bean');
Route::get('test/service', 'Test/service');
Route::get('test/create-table', 'Test/createTable');
Route::get('test/create-category-data', 'Test/createCategoryData');
Route::get('test/category', 'Test/category');

// 管理员认证路由（不需要认证的接口）
Route::group('auth', function () {
    Route::post('login', 'AuthController/login');           // 登录
    Route::get('check', 'AuthController/check');            // 检查登录状态
});

// 管理员认证路由（需要认证的接口）
Route::group('auth', function () {
    Route::post('logout', 'AuthController/logout');         // 登出
    Route::get('info', 'AuthController/info');              // 获取用户信息
})->middleware([
    \app\api\middleware\AuthMiddleware::class,
    \app\api\middleware\RoleMiddleware::class
]);

// 管理员用户管理路由（需要认证和权限）
Route::group('users', function () {
    Route::get('', 'UserController/index');                 // 用户列表
    Route::post('', 'UserController/create');               // 创建用户
    Route::get(':id', 'UserController/read');               // 获取用户详情
    Route::put(':id', 'UserController/update');             // 更新用户
    Route::delete(':id', 'UserController/delete');          // 删除用户
    Route::post(':id/password', 'UserController/changePassword'); // 修改密码
})->middleware([
    \app\api\middleware\AuthMiddleware::class,
    \app\api\middleware\RoleMiddleware::class
]);

// 文章分类管理路由（使用方法级权限控制）
Route::group('categories', function () {
    Route::get('', 'ArticleCategoryController/index');      // 分类列表
    Route::post('', 'ArticleCategoryController/create');    // 创建分类
    Route::get('tree', 'ArticleCategoryController/tree');   // 获取菜单树
    Route::get('children', 'ArticleCategoryController/children'); // 获取子分类
    Route::get('type/:type', 'ArticleCategoryController/getByType'); // 根据类型获取分类
    Route::get(':id', 'ArticleCategoryController/read');    // 获取分类详情
    Route::put(':id', 'ArticleCategoryController/update');  // 更新分类
    Route::delete(':id', 'ArticleCategoryController/delete'); // 删除分类
    Route::get(':id/breadcrumb', 'ArticleCategoryController/breadcrumb'); // 获取面包屑
    Route::post(':id/move', 'ArticleCategoryController/move'); // 移动分类
    Route::post(':id/toggle-show', 'ArticleCategoryController/toggleShow'); // 切换显示状态
    Route::post('update-sort', 'ArticleCategoryController/updateSort'); // 批量更新排序
})->middleware([
    \app\api\middleware\AuthMiddleware::class,
    \app\api\middleware\RoleMiddleware::class
]);

// 文章管理路由（使用方法级权限控制）
Route::group('articles', function () {
    Route::get('', 'ArticleController/index');                     // 文章列表
    Route::post('', 'ArticleController/create');                   // 创建文章
    Route::get('statistics', 'ArticleController/statistics');      // 文章统计
    Route::get(':id', 'ArticleController/read');                   // 获取文章详情
    Route::put(':id', 'ArticleController/update');                 // 更新文章
    Route::delete(':id', 'ArticleController/delete');              // 删除文章
    Route::post(':id/toggle-top', 'ArticleController/toggleTop');  // 切换置顶状态
    Route::post(':id/change-status', 'ArticleController/changeStatus'); // 切换文章状态
    Route::post('update-sort', 'ArticleController/updateSort');    // 批量更新排序
})->middleware([
    \app\api\middleware\AuthMiddleware::class,
    \app\api\middleware\RoleMiddleware::class
]);

// 文章自定义字段管理路由（使用方法级权限控制）
Route::group('article-custom-fields', function () {
    Route::get('', 'ArticleCustomFieldController/index');          // 字段列表
    Route::get('active', 'ArticleCustomFieldController/active');   // 获取启用字段
    Route::post('', 'ArticleCustomFieldController/create');        // 创建字段
    Route::get('statistics', 'ArticleCustomFieldController/statistics'); // 字段统计
    Route::get('type/:type', 'ArticleCustomFieldController/getByType'); // 根据类型获取字段
    Route::get(':id', 'ArticleCustomFieldController/read');        // 获取字段详情
    Route::put(':id', 'ArticleCustomFieldController/update');      // 更新字段
    Route::delete(':id', 'ArticleCustomFieldController/delete');   // 删除字段
    Route::post(':id/toggle-active', 'ArticleCustomFieldController/toggleActive'); // 切换启用状态
    Route::get(':id/usage-stats', 'ArticleCustomFieldController/usageStats'); // 字段使用统计
    Route::post('update-sort', 'ArticleCustomFieldController/updateSort'); // 批量更新排序
})->middleware([
    \app\api\middleware\AuthMiddleware::class,
    \app\api\middleware\RoleMiddleware::class
]);

// 公开分类接口（演示不同权限级别）
Route::group('public-categories', function () {
    Route::get('public/tree', 'PublicCategoryController/publicTree');     // 公开菜单树（无需权限）
    Route::get('public/list', 'PublicCategoryController/publicList');     // 公开分类列表（无需权限）
    Route::get('stats', 'PublicCategoryController/adminStats');           // 管理员统计（需要登录）
    Route::post('system/manage', 'PublicCategoryController/systemManage'); // 系统管理（需要超级管理员）
})->middleware([
    \app\api\middleware\AuthMiddleware::class,
    \app\api\middleware\RoleMiddleware::class
]);

// 门户配置管理路由
Route::group('portal', function () {
    // 基础配置管理
    Route::group('configs', function () {
        Route::get('/', 'PortalConfigController/index');                    // 获取配置列表
        Route::get('groups', 'PortalConfigController/groups');              // 获取配置分组
        Route::get('group/:group', 'PortalConfigController/getByGroup');    // 根据分组获取配置
        Route::get(':id', 'PortalConfigController/read');                   // 获取单个配置
        Route::post('/', 'PortalConfigController/create');                  // 创建配置
        Route::put(':id', 'PortalConfigController/update');                 // 更新配置
        Route::delete(':id', 'PortalConfigController/delete');              // 删除配置
        Route::post('batch', 'PortalConfigController/batchSet');            // 批量设置配置
        Route::post('clear-cache', 'PortalConfigController/clearCache');    // 清除缓存
    });

    // 模块配置管理
    Route::group('modules', function () {
        Route::get('/', 'PortalModuleController/index');                    // 获取模块列表
        Route::get(':id', 'PortalModuleController/read');                   // 获取单个模块
        Route::post('/', 'PortalModuleController/create');                  // 创建模块
        Route::put(':id', 'PortalModuleController/update');                 // 更新模块
        Route::delete(':id', 'PortalModuleController/delete');              // 删除模块
        Route::post(':id/toggle', 'PortalModuleController/toggleStatus');   // 切换模块状态
        Route::post('sort', 'PortalModuleController/updateSort');           // 更新排序
        Route::get(':module_name/config', 'PortalModuleController/getConfig'); // 获取模块配置
        Route::post(':module_name/config', 'PortalModuleController/setConfig'); // 设置模块配置
    });
})->middleware([
    \app\api\middleware\AuthMiddleware::class,
    \app\api\middleware\RoleMiddleware::class
]);

// 公开门户接口（无需权限）
Route::group('public-portal', function () {
    Route::get('modules/enabled', 'PortalModuleController/enabled');       // 获取启用的模块列表
});

// API基础路由
Route::get('/', 'Index/index');
Route::get('test', 'Index/test');
