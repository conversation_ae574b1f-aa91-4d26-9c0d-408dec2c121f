<?php
declare(strict_types=1);

namespace app\common\service;

use app\common\bean\ArticleCategoryBean;
use app\common\repository\ArticleCategoryRepository;
use think\exception\ValidateException;
use think\facade\Cache;

/**
 * 文章分类服务
 */
class ArticleCategoryService
{
    protected ArticleCategoryRepository $repository;

    // 缓存键名常量
    const CACHE_KEY_TREE = 'category_tree';
    const CACHE_KEY_LIST_VISIBLE = 'category_list_visible';
    const CACHE_KEY_TOP_LEVEL = 'category_top_level';
    const CACHE_KEY_BY_TYPE = 'category_by_type_';

    // 缓存时间（秒）
    const CACHE_TTL = 3600; // 1小时

    public function __construct()
    {
        $this->repository = new ArticleCategoryRepository();
    }
    
    /**
     * 获取分类列表
     */
    public function getList(int $page = 1, int $limit = 15, array $where = []): array
    {
        return $this->repository->getList($page, $limit, $where);
    }
    
    /**
     * 根据ID获取分类详情
     */
    public function getById(int $id): ?ArticleCategoryBean
    {
        return $this->repository->findById($id);
    }
    
    /**
     * 根据slug获取分类详情
     */
    public function getBySlug(string $slug): ?ArticleCategoryBean
    {
        return $this->repository->findBySlug($slug);
    }
    
    /**
     * 创建分类
     */
    public function create(array $data): ArticleCategoryBean
    {
        // 数据验证
        $this->validateCategoryData($data);
        
        // 检查slug是否已存在
        if ($this->repository->existsBySlug($data['slug'])) {
            throw new ValidateException('分类别名已存在');
        }
        
        // 创建Bean对象
        $category = new ArticleCategoryBean();
        $category->parentId = (int)($data['parent_id'] ?? 0);
        $category->name = $data['name'];
        $category->slug = $data['slug'];
        $category->type = $data['type'] ?? ArticleCategoryBean::TYPE_LIST;
        $category->description = $data['description'] ?? null;
        $category->linkUrl = $data['link_url'] ?? null;
        $category->coverImage = $data['cover_image'] ?? null;
        $category->sortOrder = (int)($data['sort_order'] ?? 0);
        $category->isShow = (int)($data['is_show'] ?? ArticleCategoryBean::SHOW_VISIBLE);
        $category->seoTitle = $data['seo_title'] ?? null;
        $category->seoKeywords = $data['seo_keywords'] ?? null;
        $category->seoDescription = $data['seo_description'] ?? null;
        
        // 验证父分类
        if ($category->parentId > 0) {
            $parent = $this->repository->findById($category->parentId);
            if (!$parent) {
                throw new ValidateException('父分类不存在');
            }
            
            // 检查父分类是否可以有子分类
            if (!$parent->canHaveChildren()) {
                throw new ValidateException('该父分类不支持子分类');
            }
            
            // 计算层级和路径
            $category->level = $parent->level + 1;
            $parentPath = $parent->getPathArray();
            $parentPath[] = $parent->id;
            $category->setPathArray($parentPath);
        } else {
            // 顶级分类
            $category->level = 1;
            $category->path = null;
        }
        
        // 如果没有设置排序，使用最大值+1
        if ($category->sortOrder === 0) {
            $category->sortOrder = $this->repository->getMaxSortOrder($category->parentId) + 1;
        }
        
        // 保存到数据库
        $id = $this->repository->create($category);
        
        // 更新路径（包含自己的ID）
        if ($category->parentId > 0) {
            $pathArray = $category->getPathArray();
            $pathArray[] = $id;
            $category->setPathArray($pathArray);
        } else {
            $category->setPathArray([$id]);
        }
        
        $this->repository->update($category);
        
        return $category;
    }
    
    /**
     * 更新分类
     */
    public function update(int $id, array $data): ArticleCategoryBean
    {
        $category = $this->repository->findById($id);
        if (!$category) {
            throw new ValidateException('分类不存在');
        }
        
        // 数据验证
        $this->validateCategoryData($data, true);
        
        // 检查slug是否已存在（排除自己）
        if (isset($data['slug']) && $this->repository->existsBySlug($data['slug'], $id)) {
            throw new ValidateException('分类别名已存在');
        }
        
        // 检查父分类变更
        $oldParentId = $category->parentId;
        $newParentId = isset($data['parent_id']) ? (int)$data['parent_id'] : $oldParentId;
        
        if ($newParentId !== $oldParentId) {
            // 检查循环引用
            if ($this->repository->checkCircularReference($id, $newParentId)) {
                throw new ValidateException('不能将分类移动到自己的子分类下');
            }
            
            // 验证新父分类
            if ($newParentId > 0) {
                $newParent = $this->repository->findById($newParentId);
                if (!$newParent) {
                    throw new ValidateException('新父分类不存在');
                }
                
                if (!$newParent->canHaveChildren()) {
                    throw new ValidateException('新父分类不支持子分类');
                }
            }
        }
        
        // 更新字段
        if (isset($data['name'])) $category->name = $data['name'];
        if (isset($data['slug'])) $category->slug = $data['slug'];
        if (isset($data['type'])) {
            // 检查类型变更的约束
            if ($data['type'] !== $category->type && $this->repository->hasChildren($id)) {
                if ($data['type'] !== ArticleCategoryBean::TYPE_LIST) {
                    throw new ValidateException('该分类有子分类，不能修改为非列表类型');
                }
            }
            $category->type = $data['type'];
        }
        if (isset($data['description'])) $category->description = $data['description'];
        if (isset($data['link_url'])) $category->linkUrl = $data['link_url'];
        if (isset($data['cover_image'])) $category->coverImage = $data['cover_image'];
        if (isset($data['sort_order'])) $category->sortOrder = (int)$data['sort_order'];
        if (isset($data['is_show'])) $category->isShow = (int)$data['is_show'];
        if (isset($data['seo_title'])) $category->seoTitle = $data['seo_title'];
        if (isset($data['seo_keywords'])) $category->seoKeywords = $data['seo_keywords'];
        if (isset($data['seo_description'])) $category->seoDescription = $data['seo_description'];
        
        // 处理父分类变更
        if ($newParentId !== $oldParentId) {
            $category->parentId = $newParentId;
            $this->updateCategoryPath($category);
        }
        
        $this->repository->update($category);
        
        return $category;
    }
    
    /**
     * 删除分类
     */
    public function delete(int $id): bool
    {
        $category = $this->repository->findById($id);
        if (!$category) {
            throw new ValidateException('分类不存在');
        }
        
        // 检查是否有子分类
        if ($this->repository->hasChildren($id)) {
            throw new ValidateException('该分类下还有子分类，不能删除');
        }
        
        // TODO: 检查是否有关联的文章
        // if ($this->hasArticles($id)) {
        //     throw new ValidateException('该分类下还有文章，不能删除');
        // }
        
        return $this->repository->delete($id);
    }
    
    /**
     * 获取树形菜单结构
     */
    public function getMenuTree(bool $onlyVisible = true): array
    {
        $categories = $this->repository->findAll($onlyVisible);
        return $this->buildTree($categories);
    }
    
    /**
     * 获取子分类
     */
    public function getChildren(int $parentId, bool $onlyVisible = false): array
    {
        return $this->repository->findByParentId($parentId, $onlyVisible);
    }
    
    /**
     * 获取面包屑路径
     */
    public function getBreadcrumb(int $id): array
    {
        return $this->repository->getBreadcrumb($id);
    }
    
    /**
     * 根据类型获取分类
     */
    public function getByType(string $type, bool $onlyVisible = false): array
    {
        return $this->repository->findByType($type, $onlyVisible);
    }

    /**
     * 验证分类数据
     */
    private function validateCategoryData(array $data, bool $isUpdate = false): void
    {
        // 必填字段验证（仅在创建时检查）
        if (!$isUpdate) {
            if (empty($data['name'])) {
                throw new ValidateException('分类名称不能为空');
            }

            if (empty($data['slug'])) {
                throw new ValidateException('分类别名不能为空');
            }
        }

        // 字段长度验证
        if (isset($data['name']) && mb_strlen($data['name']) > 100) {
            throw new ValidateException('分类名称不能超过100个字符');
        }

        if (isset($data['slug']) && strlen($data['slug']) > 100) {
            throw new ValidateException('分类别名不能超过100个字符');
        }

        // slug格式验证（只允许字母、数字、连字符、下划线）
        if (isset($data['slug']) && !preg_match('/^[a-zA-Z0-9_-]+$/', $data['slug'])) {
            throw new ValidateException('分类别名只能包含字母、数字、连字符和下划线');
        }

        // 类型验证
        if (isset($data['type']) && !in_array($data['type'], [
            ArticleCategoryBean::TYPE_LIST,
            ArticleCategoryBean::TYPE_SINGLE,
            ArticleCategoryBean::TYPE_LINK
        ])) {
            throw new ValidateException('无效的分类类型');
        }

        // 连接类型必须有URL
        if (isset($data['type']) && $data['type'] === ArticleCategoryBean::TYPE_LINK) {
            if (empty($data['link_url'])) {
                throw new ValidateException('连接类型分类必须设置链接地址');
            }

            if (!filter_var($data['link_url'], FILTER_VALIDATE_URL)) {
                throw new ValidateException('链接地址格式不正确');
            }
        }

        // 父分类ID验证
        if (isset($data['parent_id']) && $data['parent_id'] < 0) {
            throw new ValidateException('父分类ID不能为负数');
        }

        // 排序值验证
        if (isset($data['sort_order']) && $data['sort_order'] < 0) {
            throw new ValidateException('排序值不能为负数');
        }

        // 显示状态验证
        if (isset($data['is_show']) && !in_array($data['is_show'], [0, 1])) {
            throw new ValidateException('显示状态值无效');
        }
    }

    /**
     * 更新分类路径
     */
    private function updateCategoryPath(ArticleCategoryBean $category): void
    {
        if ($category->parentId > 0) {
            $parent = $this->repository->findById($category->parentId);
            if ($parent) {
                $category->level = $parent->level + 1;
                $parentPath = $parent->getPathArray();
                $parentPath[] = $parent->id;
                $parentPath[] = $category->id;
                $category->setPathArray($parentPath);
            }
        } else {
            $category->level = 1;
            $category->setPathArray([$category->id]);
        }

        // 更新所有子分类的路径
        $this->updateChildrenPath($category->id);
    }

    /**
     * 递归更新子分类路径
     */
    private function updateChildrenPath(int $parentId): void
    {
        $children = $this->repository->findByParentId($parentId);

        foreach ($children as $child) {
            $parent = $this->repository->findById($child->parentId);
            if ($parent) {
                $child->level = $parent->level + 1;
                $parentPath = $parent->getPathArray();
                $parentPath[] = $child->id;
                $child->setPathArray($parentPath);

                $this->repository->update($child);

                // 递归更新子分类的子分类
                $this->updateChildrenPath($child->id);
            }
        }
    }

    /**
     * 构建树形结构
     */
    private function buildTree(array $categories, int $parentId = 0): array
    {
        $tree = [];

        foreach ($categories as $category) {
            if ($category->parentId == $parentId) {
                $item = $category->toMenuItem();
                $item['children'] = $this->buildTree($categories, $category->id);
                $tree[] = $item;
            }
        }

        return $tree;
    }

    /**
     * 移动分类
     */
    public function move(int $id, int $newParentId): bool
    {
        $category = $this->repository->findById($id);
        if (!$category) {
            throw new ValidateException('分类不存在');
        }

        // 检查循环引用
        if ($this->repository->checkCircularReference($id, $newParentId)) {
            throw new ValidateException('不能将分类移动到自己的子分类下');
        }

        // 验证新父分类
        if ($newParentId > 0) {
            $newParent = $this->repository->findById($newParentId);
            if (!$newParent) {
                throw new ValidateException('新父分类不存在');
            }

            if (!$newParent->canHaveChildren()) {
                throw new ValidateException('新父分类不支持子分类');
            }
        }

        $category->parentId = $newParentId;
        $this->updateCategoryPath($category);

        return $this->repository->update($category);
    }

    /**
     * 批量更新排序
     */
    public function updateSort(array $sortData): bool
    {
        try {
            foreach ($sortData as $item) {
                if (isset($item['id']) && isset($item['sort_order'])) {
                    $category = $this->repository->findById((int)$item['id']);
                    if ($category) {
                        $category->sortOrder = (int)$item['sort_order'];
                        $this->repository->update($category);
                    }
                }
            }
            return true;
        } catch (\Exception $e) {
            throw new ValidateException('批量更新排序失败：' . $e->getMessage());
        }
    }

    /**
     * 切换显示状态
     */
    public function toggleShow(int $id): bool
    {
        $category = $this->repository->findById($id);
        if (!$category) {
            throw new ValidateException('分类不存在');
        }

        $category->isShow = $category->isShow ? 0 : 1;

        return $this->repository->update($category);
    }
}
